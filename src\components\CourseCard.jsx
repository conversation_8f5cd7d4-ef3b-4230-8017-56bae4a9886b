import { Link } from 'react-router-dom';
import { FaCertificate, FaClock, FaUsers, FaStar, FaExternalLinkAlt } from 'react-icons/fa';

const CourseCard = ({ course }) => {
  const {
    id,
    title,
    platform,
    category,
    duration,
    level,
    language,
    hasCertificate,
    certificateType,
    rating,
    enrollments,
    description,
    skills,
    instructor,
    image,
    enrollmentUrl
  } = course;

  const formatEnrollments = (num) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(0)}K`;
    return num.toString();
  };

  return (
    <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
      {/* Image */}
      <div className="relative h-48 overflow-hidden">
        <img
          src={image}
          alt={title}
          className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
        />
        {hasCertificate && (
          <div className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs flex items-center">
            <FaCertificate className="mr-1" />
            Certificat
          </div>
        )}
        <div className="absolute top-2 left-2 bg-blue-600 text-white px-2 py-1 rounded-full text-xs">
          {level}
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Category and Platform */}
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-blue-600 font-medium">{category}</span>
          <span className="text-sm text-gray-500">{platform}</span>
        </div>

        {/* Title */}
        <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
          {title}
        </h3>

        {/* Instructor */}
        <p className="text-sm text-gray-600 mb-2">Par {instructor}</p>

        {/* Description */}
        <p className="text-sm text-gray-700 mb-3 line-clamp-2">
          {description}
        </p>

        {/* Stats */}
        <div className="flex items-center justify-between mb-3 text-sm text-gray-600">
          <div className="flex items-center">
            <FaStar className="text-yellow-400 mr-1" />
            <span>{rating}</span>
          </div>
          <div className="flex items-center">
            <FaUsers className="mr-1" />
            <span>{formatEnrollments(enrollments)}</span>
          </div>
          <div className="flex items-center">
            <FaClock className="mr-1" />
            <span>{duration}</span>
          </div>
        </div>

        {/* Skills */}
        <div className="mb-3">
          <div className="flex flex-wrap gap-1">
            {skills.slice(0, 3).map((skill, index) => (
              <span
                key={index}
                className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
              >
                {skill}
              </span>
            ))}
            {skills.length > 3 && (
              <span className="text-xs text-gray-500">+{skills.length - 3}</span>
            )}
          </div>
        </div>

        {/* Certificate Info */}
        {hasCertificate && (
          <div className="mb-3 p-2 bg-green-50 rounded border-l-4 border-green-400">
            <p className="text-sm text-green-700">
              <FaCertificate className="inline mr-1" />
              Certificat {certificateType}
            </p>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-2">
          <Link
            to={`/course/${id}`}
            className="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded hover:bg-blue-700 transition-colors text-sm"
          >
            Voir détails
          </Link>
          <a
            href={enrollmentUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center justify-center bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 transition-colors text-sm"
          >
            <FaExternalLinkAlt className="mr-1" />
            S'inscrire
          </a>
        </div>
      </div>
    </div>
  );
};

export default CourseCard;
