import { useState, useEffect } from 'react';
import { courses } from '../data/courses';
import CourseCard from '../components/CourseCard';
import CategoryFilter from '../components/CategoryFilter';
import { FaGraduationCap, FaCertificate, FaUsers, FaGlobe } from 'react-icons/fa';

const Home = ({ searchTerm }) => {
  const [filteredCourses, setFilteredCourses] = useState(courses);
  const [selectedCategory, setSelectedCategory] = useState('Tous');

  useEffect(() => {
    let filtered = courses;

    // Filtrer par catégorie
    if (selectedCategory !== 'Tous') {
      filtered = filtered.filter(course => course.category === selectedCategory);
    }

    // Filtrer par terme de recherche
    if (searchTerm) {
      filtered = filtered.filter(course =>
        course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase())) ||
        course.platform.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredCourses(filtered);
  }, [selectedCategory, searchTerm]);

  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
  };

  const stats = {
    totalCourses: courses.length,
    totalEnrollments: courses.reduce((sum, course) => sum + course.enrollments, 0),
    certificateCourses: courses.filter(course => course.hasCertificate).length,
    platforms: [...new Set(courses.map(course => course.platform))].length
  };

  const formatNumber = (num) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(0)}K`;
    return num.toString();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Apprenez gratuitement avec des certificats
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            Découvrez les meilleurs cours gratuits avec certificat des plus grandes plateformes éducatives mondiales
          </p>
          
          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="flex justify-center mb-2">
                <FaGraduationCap className="text-3xl" />
              </div>
              <div className="text-2xl font-bold">{stats.totalCourses}</div>
              <div className="text-sm opacity-90">Cours disponibles</div>
            </div>
            <div className="text-center">
              <div className="flex justify-center mb-2">
                <FaCertificate className="text-3xl" />
              </div>
              <div className="text-2xl font-bold">{stats.certificateCourses}</div>
              <div className="text-sm opacity-90">Avec certificat</div>
            </div>
            <div className="text-center">
              <div className="flex justify-center mb-2">
                <FaUsers className="text-3xl" />
              </div>
              <div className="text-2xl font-bold">{formatNumber(stats.totalEnrollments)}</div>
              <div className="text-sm opacity-90">Étudiants inscrits</div>
            </div>
            <div className="text-center">
              <div className="flex justify-center mb-2">
                <FaGlobe className="text-3xl" />
              </div>
              <div className="text-2xl font-bold">{stats.platforms}</div>
              <div className="text-sm opacity-90">Plateformes</div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="container mx-auto px-4 py-12">
        {/* Search Results Info */}
        {searchTerm && (
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Résultats de recherche pour "{searchTerm}"
            </h2>
            <p className="text-gray-600">
              {filteredCourses.length} cours trouvé{filteredCourses.length > 1 ? 's' : ''}
            </p>
          </div>
        )}

        {/* Category Filter */}
        <CategoryFilter
          selectedCategory={selectedCategory}
          onCategoryChange={handleCategoryChange}
        />

        {/* Course Grid */}
        {filteredCourses.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCourses.map(course => (
              <CourseCard key={course.id} course={course} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <FaGraduationCap className="text-6xl mx-auto" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Aucun cours trouvé
            </h3>
            <p className="text-gray-600">
              Essayez de modifier vos critères de recherche ou de sélectionner une autre catégorie.
            </p>
          </div>
        )}
      </section>
    </div>
  );
};

export default Home;
