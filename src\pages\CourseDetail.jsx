import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { courses } from '../data/courses';
import { 
  FaCertificate, 
  FaClock, 
  FaUsers, 
  FaStar, 
  FaExternalLinkAlt, 
  FaArrowLeft,
  FaGlobe,
  FaCalendarAlt,
  FaUser,
  FaTag
} from 'react-icons/fa';

const CourseDetail = () => {
  const { id } = useParams();
  const course = courses.find(c => c.id === parseInt(id));

  if (!course) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Cours non trouvé</h2>
          <Link to="/" className="text-blue-600 hover:text-blue-800">
            Retour à l'accueil
          </Link>
        </div>
      </div>
    );
  }

  const formatEnrollments = (num) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(0)}K`;
    return num.toString();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Back Button */}
        <Link
          to="/"
          className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-6"
        >
          <FaArrowLeft className="mr-2" />
          Retour aux cours
        </Link>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Course Image */}
            <div className="relative h-64 md:h-80 rounded-lg overflow-hidden mb-6">
              <img
                src={course.image}
                alt={course.title}
                className="w-full h-full object-cover"
              />
              {course.hasCertificate && (
                <div className="absolute top-4 right-4 bg-green-500 text-white px-3 py-2 rounded-full flex items-center">
                  <FaCertificate className="mr-2" />
                  Certificat disponible
                </div>
              )}
            </div>

            {/* Course Title and Basic Info */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <div className="flex flex-wrap items-center gap-2 mb-4">
                <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                  {course.category}
                </span>
                <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm">
                  {course.level}
                </span>
                <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">
                  {course.language}
                </span>
              </div>

              <h1 className="text-3xl font-bold text-gray-900 mb-4">{course.title}</h1>
              
              <div className="flex items-center text-gray-600 mb-4">
                <FaUser className="mr-2" />
                <span>Par {course.instructor}</span>
                <span className="mx-4">•</span>
                <span>{course.platform}</span>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="text-center p-3 bg-gray-50 rounded">
                  <FaStar className="text-yellow-400 mx-auto mb-1" />
                  <div className="font-semibold">{course.rating}</div>
                  <div className="text-sm text-gray-600">Note</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded">
                  <FaUsers className="text-blue-500 mx-auto mb-1" />
                  <div className="font-semibold">{formatEnrollments(course.enrollments)}</div>
                  <div className="text-sm text-gray-600">Étudiants</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded">
                  <FaClock className="text-green-500 mx-auto mb-1" />
                  <div className="font-semibold">{course.duration}</div>
                  <div className="text-sm text-gray-600">Durée</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded">
                  <FaGlobe className="text-purple-500 mx-auto mb-1" />
                  <div className="font-semibold">{course.language}</div>
                  <div className="text-sm text-gray-600">Langue</div>
                </div>
              </div>

              {/* Description */}
              <div className="mb-6">
                <h3 className="text-xl font-semibold mb-3">Description du cours</h3>
                <p className="text-gray-700 leading-relaxed">{course.description}</p>
              </div>

              {/* Skills */}
              <div className="mb-6">
                <h3 className="text-xl font-semibold mb-3">Compétences acquises</h3>
                <div className="flex flex-wrap gap-2">
                  {course.skills.map((skill, index) => (
                    <span
                      key={index}
                      className="bg-blue-50 text-blue-700 px-3 py-2 rounded-lg text-sm font-medium"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Enrollment Card */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6 sticky top-4">
              <div className="text-center mb-6">
                <div className="text-3xl font-bold text-green-600 mb-2">GRATUIT</div>
                {course.hasCertificate && (
                  <div className="text-sm text-gray-600">
                    Certificat {course.certificateType}
                  </div>
                )}
              </div>

              <a
                href={course.enrollmentUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center text-lg font-semibold mb-4"
              >
                <FaExternalLinkAlt className="mr-2" />
                S'inscrire maintenant
              </a>

              <div className="text-center text-sm text-gray-600 mb-6">
                Redirection vers {course.platform}
              </div>

              {/* Course Info */}
              <div className="space-y-3">
                <div className="flex items-center">
                  <FaCalendarAlt className="text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm text-gray-600">Date de début</div>
                    <div className="font-medium">{course.startDate}</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <FaClock className="text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm text-gray-600">Durée</div>
                    <div className="font-medium">{course.duration}</div>
                  </div>
                </div>

                <div className="flex items-center">
                  <FaTag className="text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm text-gray-600">Niveau</div>
                    <div className="font-medium">{course.level}</div>
                  </div>
                </div>

                {course.hasCertificate && (
                  <div className="flex items-center">
                    <FaCertificate className="text-gray-400 mr-3" />
                    <div>
                      <div className="text-sm text-gray-600">Certificat</div>
                      <div className="font-medium">{course.certificateType}</div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Certificate Info */}
            {course.hasCertificate && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <FaCertificate className="text-green-600 mr-2" />
                  <h4 className="font-semibold text-green-800">Certificat gratuit</h4>
                </div>
                <p className="text-sm text-green-700">
                  Obtenez un certificat officiel à la fin de ce cours. 
                  Parfait pour enrichir votre CV et LinkedIn !
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseDetail;
